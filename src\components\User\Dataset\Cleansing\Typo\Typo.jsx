import React, { useState } from "react";
import CleansingTypos from "./CleansingTypos";
import ApplyTable from "../ApplyTable";
import { useSelector } from "react-redux";

const Typo = () => {
  const [showTable, setShowTable] = useState(false);
  const [resetParameter, setResetParameter] = useState(false);
  const dataset = useSelector((state) => state.upload.dataset);

  const handleApply = () => setShowTable(true);
  const handleResetParameters = () => {
    setResetParameter(true);
    setShowTable(false);
  };

  return (
    <div className="max-w-3xl mx-auto p-8 bg-white rounded-2xl shadow-lg border border-gray-100 space-y-8 font-sans">
      <h1 className="text-2xl font-bold text-blue-700 mb-2 flex items-center gap-2">
        <span className="inline-block w-2 h-8 bg-blue-400 rounded-full mr-2"></span>
        Suggestion
      </h1>

      <CleansingTypos
        resetParameter={resetParameter}
        setResetParameter={setResetParameter}
      />

      <div className="flex flex-col items-center gap-5 p-5 w-full">
        <div className="flex gap-5">
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[200px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleApply}
          >
            Apply Typo Correction
          </button>
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[200px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleResetParameters}
          >
            Reset Parameters
          </button>
        </div>
      </div>

      {showTable && (
        <ApplyTable originalData={dataset} cleanedData={dataset} tab="Typo" />
      )}
    </div>
  );
};

export default Typo;
