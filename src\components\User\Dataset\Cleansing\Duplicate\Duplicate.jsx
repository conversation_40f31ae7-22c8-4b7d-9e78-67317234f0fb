import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useSelector } from "react-redux";
import Dropdown from "../dropdown";
import CustomSlider from "../CustomSlider";
import ApplyTable from "../ApplyTable";

const aggregationOptions = ["sum", "mean", "median", "count", "mode"];
const similarityClusteringMethods = ["Euclidean Distance", "Cosine Similarity"];
const textSimilarityMethods = [
  "Levenshtein Distance",
  "Jaccard Similarity",
  "Fuzzy Matching",
  "Cosine Similarity",
];

const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

const Duplicate = () => {
  const dataset = useSelector((state) => state.upload.dataset);
  const features = useSelector((s) => s.upload.features) || {
    numerical: { items: [] },
    nominal: { items: [] },
    ordinal: { items: [] },
  };

  const allFeatures = [
    ...features.numerical.items,
    ...features.nominal.items,
    ...features.ordinal.items,
  ];
  const nominalOrdinalItems = [
    ...features.nominal.items,
    ...features.ordinal.items,
  ];

  const allFeaturesMap = useMemo(
    () => Object.fromEntries(allFeatures.map((i) => [i.id, i])),
    [allFeatures]
  );
  const nominalOrdinalItemsMap = useMemo(
    () => Object.fromEntries(nominalOrdinalItems.map((i) => [i.id, i])),
    [nominalOrdinalItems]
  );

  const allFeatureOrder = useMemo(() => allFeatures.map((i) => i.id), [allFeatures]);
  const nominalOrdinalFeatureOrder = useMemo(
    () => nominalOrdinalItems.map((i) => i.id),
    [nominalOrdinalItems]
  );

  const [selectedDuplicateOption, setSelectedDuplicateOption] = useState("Remove Duplicates");
  const [removeDuplicatesFeatures, setRemoveDuplicatesFeatures] = useState([]);
  const [duplicateKeepOption, setDuplicateKeepOption] = useState("first");
  const [groupByFeatures, setGroupByFeatures] = useState([]);
  const [aggregationSelections, setAggregationSelections] = useState({});
  const [selectedSimilarityFeatures, setSelectedSimilarityFeatures] = useState([]);
  const [selectedSimilarityClusteringMethod, setSelectedSimilarityClusteringMethod] = useState("");
  const [similarityClusteringThreshold, setSimilarityClusteringThreshold] = useState(0);
  const [euclideanDistance, setEuclideanDistance] = useState({
    threshold: 0.5,
    scaling: "zscore",
    metric: "euclidean",
  });
  const [selectedTextDuplicateFeatures, setSelectedTextDuplicateFeatures] = useState([]);
  const [selectedTextSimilarityMethod, setSelectedTextSimilarityMethod] = useState("");
  const [textSimilarityThreshold, setTextSimilarityThreshold] = useState(0);
  const [showTable, setShowTable] = useState(false);

  const getAggregationOptionsForFeature = useCallback(
    (featureId) => {
      if (features.numerical?.items.find((item) => item.id === featureId)) {
        return aggregationOptions;
      } else if (features.nominal?.items.find((item) => item.id === featureId)) {
        return ["count", "mode"];
      } else if (features.ordinal?.items.find((item) => item.id === featureId)) {
        return ["median", "mode", "count"];
      } else {
        return aggregationOptions;
      }
    },
    [features.numerical?.items, features.nominal?.items, features.ordinal?.items]
  );

  const computeInitialAggregationSelections = useCallback(() => {
    const initial = {};
    const aggregateFeatures = allFeatureOrder.filter((id) => !groupByFeatures.includes(id));
    aggregateFeatures.forEach((featureId) => {
      const availableOptions = getAggregationOptionsForFeature(featureId);
      if (aggregationSelections[featureId] && availableOptions.includes(aggregationSelections[featureId])) {
        initial[featureId] = aggregationSelections[featureId];
      } else if (features.numerical?.items.find((item) => item.id === featureId)) {
        initial[featureId] = "mean";
      } else if (features.nominal?.items.find((item) => item.id === featureId)) {
        initial[featureId] = "mode";
      } else if (features.ordinal?.items.find((item) => item.id === featureId)) {
        initial[featureId] = "median";
      } else {
        initial[featureId] = "";
      }
    });
    return initial;
  }, [groupByFeatures, aggregationSelections, features.numerical?.items, features.nominal?.items, features.ordinal?.items, allFeatureOrder, getAggregationOptionsForFeature]);

  useEffect(() => {
    setAggregationSelections(computeInitialAggregationSelections());
  }, [groupByFeatures, computeInitialAggregationSelections]);

  const handleOptionChange = (value) => setSelectedDuplicateOption(value);
  const handleRemoveDuplicatesFeatureToggle = (featureName) => {
    setRemoveDuplicatesFeatures((prev) =>
      prev.includes(featureName) ? prev.filter((id) => id !== featureName) : [...prev, featureName]
    );
  };
  const handleGroupByChange = (featureId) => {
    setGroupByFeatures((prev) =>
      prev.includes(featureId) ? prev.filter((id) => id !== featureId) : [...prev, featureId]
    );
  };
  const handleSimilarityClusteringFeatureChange = (id) => {
    setSelectedSimilarityFeatures((prev) =>
      prev.includes(id) ? prev.filter((fid) => fid !== id) : [...prev, id]
    );
  };
  const handleSimilarityMethodChange = (value) => setSelectedSimilarityClusteringMethod(value);
  const handleTextSimilarityMethodChange = (value) => setSelectedTextSimilarityMethod(value);

  const handleApply = () => setShowTable(true);
  const handleResetParameters = () => {
    setSelectedDuplicateOption("Remove Duplicates");
    setRemoveDuplicatesFeatures([]);
    setDuplicateKeepOption("first");
    setGroupByFeatures([]);
    setAggregationSelections({});
    setSelectedSimilarityFeatures([]);
    setSelectedSimilarityClusteringMethod("");
    setSimilarityClusteringThreshold(0);
    setSelectedTextDuplicateFeatures([]);
    setSelectedTextSimilarityMethod("");
    setTextSimilarityThreshold(0);
    setShowTable(false);
  };

  const renderSimilarityContent = () => {
    switch (selectedSimilarityClusteringMethod) {
      case "Euclidean Distance":
        return (
          <div className="mt-4 space-y-4">
            <CustomSlider
              text="Distance Threshold (ε)"
              min={0.0}
              max={5.0}
              step={0.1}
              defaultValue={euclideanDistance.threshold}
              onChange={(value) => setEuclideanDistance({ ...euclideanDistance, threshold: value })}
            />
            <div>
              <label className="block font-medium mb-2">Feature Scaling:</label>
              <select
                value={euclideanDistance.scaling}
                onChange={(e) => setEuclideanDistance({ ...euclideanDistance, scaling: e.target.value })}
                className="w-full p-2 border rounded-lg"
              >
                <option value="none">No Scaling</option>
                <option value="minmax">Min-Max (0-1)</option>
                <option value="zscore">Z-Score Standardization</option>
              </select>
            </div>
            <div>
              <label className="block font-medium mb-2">Distance Metric:</label>
              <select
                value={euclideanDistance.metric}
                onChange={(e) => setEuclideanDistance({ ...euclideanDistance, metric: e.target.value })}
                className="w-full p-2 border rounded-lg"
              >
                <option value="euclidean">Euclidean (L2)</option>
                <option value="manhattan">Manhattan (L1)</option>
              </select>
            </div>
          </div>
        );
      case "Cosine Similarity":
        return (
          <div className="mt-4">
            <h2 className="font-bold mb-2 text-lg">Similarity threshold:</h2>
            <CustomSlider
              min={0.0}
              max={1.0}
              step={0.1}
              defaultValue={similarityClusteringThreshold}
              onChange={(value) => setSimilarityClusteringThreshold(value)}
            />
          </div>
        );
      default:
        return null;
    }
  };

  const renderContent = () => {
    switch (selectedDuplicateOption) {
      case "Remove Duplicates":
        return (
          <div className="bg-white rounded-xl shadow p-6 mt-4">
            <h1 className="font-bold mb-4 text-xl text-blue-700">Select features to identify duplicates:</h1>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {allFeatureOrder
                .filter((id) => !allFeaturesMap[id].disabled)
                .map((id) => {
                  const item = allFeaturesMap[id];
                  return (
                    <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-3 py-2 shadow">
                      <input
                        type="checkbox"
                        id={item.id}
                        className="accent-blue-500"
                        checked={removeDuplicatesFeatures.includes(item.feature)}
                        onChange={() => handleRemoveDuplicatesFeatureToggle(item.feature)}
                      />
                      <span>{item.feature}</span>
                    </label>
                  );
                })}
            </div>
            <h2 className="font-semibold text-lg mt-6 mb-2">Which duplicates to keep?</h2>
            <div className="flex gap-6">
              {["first", "last", "remove"].map((option) => (
                <label key={option} className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="duplicateKeepOption"
                    value={option}
                    checked={duplicateKeepOption === option}
                    onChange={(e) => setDuplicateKeepOption(e.target.value)}
                    className="accent-blue-500"
                  />
                  {option === "first"
                    ? "Keep First Occurrence"
                    : option === "last"
                    ? "Keep Last Occurrence"
                    : "Remove All Duplicates"}
                </label>
              ))}
            </div>
          </div>
        );
      case "Aggregate Duplicates":
        const aggregateFeatures = allFeatureOrder.filter((id) => !groupByFeatures.includes(id));
        return (
          <div className="bg-white rounded-xl shadow p-6 mt-4">
            <h1 className="font-bold mb-4 text-xl text-blue-700">Select features to group by:</h1>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {allFeatureOrder
                .filter((id) => !allFeaturesMap[id].disabled)
                .map((id) => {
                  const item = allFeaturesMap[id];
                  return (
                    <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-3 py-2 shadow">
                      <input
                        type="checkbox"
                        id={item.id}
                        className="accent-blue-500"
                        checked={groupByFeatures.includes(item.id)}
                        onChange={() => handleGroupByChange(id)}
                      />
                      <span>{item.feature}</span>
                    </label>
                  );
                })}
            </div>
            <h2 className="font-bold mt-6 mb-2 text-lg">Select features to aggregate and their function:</h2>
            <div className="flex flex-col space-y-4 max-w-md">
              {aggregateFeatures.map((featureId) => {
                const featureName = allFeaturesMap[featureId]?.feature || featureId;
                const availableOptions = getAggregationOptionsForFeature(featureId);
                return (
                  <div key={featureId} className="flex items-center space-x-4">
                    <span className="w-32 font-medium capitalize">{featureName}</span>
                    <Dropdown
                      options={availableOptions}
                      value={aggregationSelections[featureId] || ""}
                      setValue={(value) => {
                        setAggregationSelections((prev) => ({
                          ...prev,
                          [featureId]: value,
                        }));
                      }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        );
      case "Use Similarity":
        return (
          <div className="bg-white rounded-xl shadow p-6 mt-4">
            <h1 className="font-bold mb-4 text-xl text-blue-700">Select features for similarity detection:</h1>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {allFeatureOrder
                .filter((id) => !allFeaturesMap[id].disabled)
                .map((id) => {
                  const item = allFeaturesMap[id];
                  return (
                    <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-3 py-2 shadow">
                      <input
                        type="checkbox"
                        id={item.id}
                        className="accent-blue-500"
                        checked={selectedSimilarityFeatures.includes(id)}
                        onChange={() => handleSimilarityClusteringFeatureChange(id)}
                      />
                      <span>{item.feature}</span>
                    </label>
                  );
                })}
            </div>
            <Dropdown
              options={similarityClusteringMethods}
              tell="Select similarity metric:"
              placeholder="Select similarity metric"
              value={selectedSimilarityClusteringMethod}
              setValue={handleSimilarityMethodChange}
            />
            {renderSimilarityContent()}
          </div>
        );
      case "Handle Duplicates in Text":
        return (
          <div className="bg-white rounded-xl shadow p-6 mt-4">
            <h1 className="font-bold mb-4 text-xl text-blue-700">Select Categorical feature to analyze:</h1>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {nominalOrdinalFeatureOrder
                .filter((id) => !nominalOrdinalItemsMap[id].disabled)
                .map((id) => {
                  const item = nominalOrdinalItemsMap[id];
                  return (
                    <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-3 py-2 shadow">
                      <input
                        type="checkbox"
                        name="categoricalFeature"
                        id={item.id}
                        className="accent-blue-500"
                        checked={selectedTextDuplicateFeatures.includes(id)}
                        onChange={(e) => {
                          let updated;
                          if (e.target.checked) {
                            updated = [...selectedTextDuplicateFeatures, id];
                          } else {
                            updated = selectedTextDuplicateFeatures.filter((fid) => fid !== id);
                          }
                          setSelectedTextDuplicateFeatures(updated);
                        }}
                      />
                      <span>{item.feature}</span>
                    </label>
                  );
                })}
            </div>
            <h2 className="font-bold mt-6 mb-2 text-lg">Select text similarity method:</h2>
            <Dropdown
              options={textSimilarityMethods}
              value={selectedTextSimilarityMethod}
              setValue={handleTextSimilarityMethodChange}
              searchPlaceholder="Select method"
            />
            <h2 className="font-bold mt-6 mb-2 text-lg">Text similarity threshold:</h2>
            <CustomSlider
              min={0.0}
              max={1.0}
              step={0.1}
              defaultValue={textSimilarityThreshold}
              onChange={(value) => setTextSimilarityThreshold(value)}
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-5xl mx-auto p-6">
      <div className="bg-white rounded-xl shadow p-6 mb-6">
        <Dropdown
          options={[
            "Remove Duplicates",
            "Aggregate Duplicates",
            "Use Similarity",
            "Handle Duplicates in Text",
          ]}
          tell="Select duplicate handling method:"
          placeholder="Select duplicate handling method"
          value={selectedDuplicateOption}
          setValue={handleOptionChange}
          searchPlaceholder="Select duplicate handling method"
        />
      </div>
      {renderContent()}
      <div className="flex flex-col items-center gap-5 p-5 w-full">
        <div className="flex gap-5">
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleApply}
          >
            Apply Duplicate Handling
          </button>
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleResetParameters}
          >
            Reset Parameters
          </button>
        </div>
      </div>
      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Duplicate"
        />
      )}
    </div>
  );
};

export default Duplicate;
