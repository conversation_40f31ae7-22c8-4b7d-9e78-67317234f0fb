import React, { useState, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";

/**
 * Component for managing and correcting typos in dataset columns
 */
const CleansingTypos = ({ resetParameter, setResetParameter }) => {
  const { dataset, features: reduxFeatures } = useSelector(
    (state) => state.upload
  );

  const [columns, setColumns] = useState([]);
  const [selectedColumn, setSelectedColumn] = useState("");
  const [uniqueValues, setUniqueValues] = useState([]);
  const [corrections, setCorrections] = useState({});

  // Collect categorical features
  const features = useMemo(
    () => [...reduxFeatures.nominal.items, ...reduxFeatures.ordinal.items],
    [reduxFeatures.nominal.items, reduxFeatures.ordinal.items]
  );

  // Only show columns that are categorical features
  useEffect(() => {
    if (dataset.length > 0) {
      const allColumns = Object.keys(dataset[0]);
      const filteredColumns = allColumns.filter((col) =>
        features.some((feature) => feature.feature === col)
      );
      setColumns(filteredColumns);
    }
  }, [dataset, features]);

  const handleColumnSelect = (column) => {
    setSelectedColumn(column);
    setResetParameter(false);
  };

  // Update unique values and corrections when column changes
  useEffect(() => {
    if (selectedColumn) {
      const values = dataset.map((item) => item[selectedColumn]);
      const unique = [...new Set(values)];
      setUniqueValues(unique);
      const newCorrections = Object.fromEntries(
        unique.map((val) => [val, val])
      );
      setCorrections(newCorrections);
    } else {
      setUniqueValues([]);
      setCorrections({});
    }
  }, [selectedColumn]);

  const handleCorrectionChange = (value, original) => {
    setCorrections((prev) => ({
      ...prev,
      [original]: value,
    }));
  };

  useEffect(() => {
    if (resetParameter) {
      setSelectedColumn("");
      setUniqueValues([]);
      setCorrections({});
    }
  }, [resetParameter]);

  return (
    <div className="max-w-3xl mx-auto my-10 p-8 bg-white rounded-2xl shadow-lg border border-gray-100 space-y-8 font-sans">
      <h2 className="text-2xl font-bold text-blue-700 mb-2 flex items-center gap-2">
        <span className="inline-block w-2 h-8 bg-blue-400 rounded-full mr-2"></span>
        Manual Typo Correction
      </h2>

      <div>
        <label className="block font-medium mb-2 text-gray-700">
          Select Column:
        </label>
        <select
          className="border border-gray-300 p-3 rounded-lg w-full text-base focus:ring-2 focus:ring-blue-400 transition"
          value={selectedColumn}
          onChange={(e) => handleColumnSelect(e.target.value)}
        >
          <option value="">-- Choose Column --</option>
          {columns.map((col) => (
            <option key={col} value={col}>
              {col}
            </option>
          ))}
        </select>
      </div>

      {selectedColumn && (
        <div>
          <h3 className="text-lg font-semibold mb-4 text-blue-600">
            Correct Typos
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {uniqueValues.map((value) => (
              <div
                key={value}
                className="flex items-center gap-3 bg-gray-50 rounded-lg p-3 shadow"
              >
                <span className="w-1/2 truncate font-medium text-gray-700">
                  {value}
                </span>
                <input
                  type="text"
                  className="border p-2 rounded-lg w-1/2 text-base focus:ring-2 focus:ring-blue-400 transition"
                  value={corrections[value]}
                  onChange={(e) =>
                    handleCorrectionChange(e.target.value, value)
                  }
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CleansingTypos;
