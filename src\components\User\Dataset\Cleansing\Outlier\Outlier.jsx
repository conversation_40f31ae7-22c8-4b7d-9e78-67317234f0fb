import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Dropdown from "../dropdown";
import CustomSlider from "../CustomSlider";
import ApplyTable from "../ApplyTable";

const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

const Outlier = () => {
  const uploadFeatures = useSelector((state) => state.upload.features);
  const dataset = useSelector((state) => state.upload.dataset);

  const [features, setFeatures] = useState([]);
  const [selectedOutlierOption, setSelectedOutlierOption] = useState("");
  const [selectedTrimmingOption, setSelectedTrimmingOption] = useState("");
  const [selectedCappingOption, setSelectedCappingOption] = useState("");
  const [zScore, setZScore] = useState(3);
  const [iqr, setIQR] = useState(1.5);
  const [percentile, setPercentile] = useState({ lower: 1, upper: 99 });
  const [powerTransformation, setPowerTransformation] = useState({
    transformation: "",
    lambda: 1,
  });
  const [showLampda, setShowLampda] = useState(false);
  const [showTable, setShowTable] = useState(false);

  const numericalItems = uploadFeatures.numerical.items;
  const allNumericalItemsMap = {};
  numericalItems.forEach((item) => {
    allNumericalItemsMap[item.id] = item;
  });
  const allNumericalFeatureOrder = numericalItems.map((item) => item.id);

  const allSelectedFeaturesPositive = () => {
    if (features.length === 0) return false;
    return features.every((featureName) => {
      const item = numericalItems.find((f) => f.feature === featureName);
      if (!item || !item.uniqueValue) return false;
      return item.uniqueValue.every((v) => typeof v === "number" && v > 0);
    });
  };

  const handleOptionChange = (value) => setSelectedOutlierOption(value);
  const handleTrimmingOptionChange = (value) => setSelectedTrimmingOption(value);
  const handleCappingOptionChange = (value) => setSelectedCappingOption(value);
  const handleThresholdZscoreValueChange = (value) => setZScore(value);
  const handleThresholdIQRValueChange = (value) => setIQR(value);
  const handlePercentileLowerValue = (value) =>
    setPercentile({ ...percentile, lower: value });
  const handlePercentileUpperValue = (value) =>
    setPercentile({ ...percentile, upper: value });
  const handleLampdaChange = (value) => setShowLampda(value);
  const handleLampdaValueChange = (value) =>
    setPowerTransformation({ ...powerTransformation, lambda: value });

  const handleFeatureCheckboxChange = (featureName, isChecked) => {
    if (isChecked) {
      if (!features.includes(featureName)) setFeatures([...features, featureName]);
    } else {
      setFeatures(features.filter((f) => f !== featureName));
    }
  };

  const handleApply = () => setShowTable(true);
  const handleResetParameters = () => {
    setFeatures([]);
    setSelectedOutlierOption("");
    setSelectedTrimmingOption("");
    setSelectedCappingOption("");
    setZScore(3);
    setIQR(1.5);
    setPercentile({ lower: 1, upper: 99 });
    setPowerTransformation({ transformation: "", lambda: 1 });
    setShowLampda(false);
    setShowTable(false);
  };

  const renderTrimmingContent = () => {
    switch (selectedTrimmingOption) {
      case "Z-score":
        return (
          <CustomSlider
            text="Z-Score Threshold"
            unit=""
            defaultValue={zScore}
            min={1}
            max={5}
            step={1}
            onChange={handleThresholdZscoreValueChange}
          />
        );
      case "IQR":
        return (
          <CustomSlider
            text="IQR Multiplier (k)"
            unit=""
            defaultValue={iqr}
            min={0}
            max={10}
            step={0.5}
            onChange={handleThresholdIQRValueChange}
          />
        );
      case "Percentile":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CustomSlider
              text="Lower Percentile (%)"
              unit=""
              defaultValue={percentile.lower}
              min={0}
              max={50}
              step={1}
              onChange={handlePercentileLowerValue}
            />
            <CustomSlider
              text="Upper Percentile (%)"
              unit=""
              defaultValue={percentile.upper}
              min={50}
              max={100}
              step={1}
              onChange={handlePercentileUpperValue}
            />
          </div>
        );
      default:
        return null;
    }
  };

  const renderCappingContent = () => {
    switch (selectedCappingOption) {
      case "Z-score":
        return (
          <CustomSlider
            text="Z-Score Threshold"
            unit=""
            defaultValue={zScore}
            min={1}
            max={5}
            step={1}
            onChange={handleThresholdZscoreValueChange}
          />
        );
      case "IQR":
        return (
          <CustomSlider
            text="IQR Multiplier (k)"
            unit=""
            defaultValue={iqr}
            min={0}
            max={10}
            step={0.5}
            onChange={handleThresholdIQRValueChange}
          />
        );
      case "Percentile":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CustomSlider
              text="Lower Percentile (%)"
              unit=""
              defaultValue={percentile.lower}
              min={0}
              max={50}
              step={1}
              onChange={handlePercentileLowerValue}
            />
            <CustomSlider
              text="Upper Percentile (%)"
              unit=""
              defaultValue={percentile.upper}
              min={50}
              max={100}
              step={1}
              onChange={handlePercentileUpperValue}
            />
          </div>
        );
      default:
        return null;
    }
  };

  const renderContent = () => {
    switch (selectedOutlierOption) {
      case "Trimming":
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-4">
            <Dropdown
              options={["Z-score", "IQR", "Percentile"]}
              placeholder="Select method"
              tell="Select Trimming Method:"
              searchPlaceholder="Search method..."
              value={selectedTrimmingOption}
              setValue={handleTrimmingOptionChange}
            />
            <div className="mt-4">{renderTrimmingContent()}</div>
          </div>
        );
      case "Capping":
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-4">
            <Dropdown
              options={["Z-score", "IQR", "Percentile"]}
              placeholder="Select method"
              tell="Select Capping Method:"
              searchPlaceholder="Search method..."
              value={selectedCappingOption}
              setValue={handleCappingOptionChange}
            />
            <div className="mt-4">{renderCappingContent()}</div>
          </div>
        );
      case "Power Transformation":
        const boxCoxDisabled = !allSelectedFeaturesPositive();
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-4">
            <h2 className="font-bold text-xl mb-4">Select Transformation</h2>
            <div className="flex gap-6 mb-4">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="transformation"
                  value="Yeo-Johnson"
                  checked={powerTransformation.transformation === "Yeo-Johnson"}
                  onChange={() =>
                    setPowerTransformation({
                      ...powerTransformation,
                      transformation: "Yeo-Johnson",
                    })
                  }
                  className="accent-blue-500"
                />
                Yeo-Johnson
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="transformation"
                  value="Box-Cox"
                  checked={powerTransformation.transformation === "Box-Cox"}
                  onChange={() =>
                    setPowerTransformation({
                      ...powerTransformation,
                      transformation: "Box-Cox",
                    })
                  }
                  disabled={boxCoxDisabled}
                  className="accent-blue-500"
                />
                Box-Cox
              </label>
            </div>
            {boxCoxDisabled && (
              <div className="text-red-500 text-sm mb-2">
                Box-Cox is only available when all selected features have positive values.
              </div>
            )}
            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                id="threshold-checkbox"
                checked={showLampda}
                onChange={() => handleLampdaChange(!showLampda)}
                className="mr-2 accent-blue-500"
              />
              <label htmlFor="threshold-checkbox" className="text-lg">
                Use Lambda (optional)
              </label>
            </div>
            {showLampda && (
              <div className="mt-4">
                <CustomSlider
                  text="Lambda Threshold"
                  unit=""
                  defaultValue={powerTransformation.lambda}
                  min={-5.0}
                  max={5.0}
                  step={0.1}
                  onChange={handleLampdaValueChange}
                />
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    if (!showTable) {
      setFeatures([]);
      setSelectedOutlierOption("");
      setSelectedTrimmingOption("");
      setSelectedCappingOption("");
      setZScore(3);
      setIQR(1.5);
      setPercentile({ lower: 1, upper: 99 });
      setPowerTransformation({ transformation: "", lambda: 1 });
      setShowLampda(false);
    }
  }, [showTable]);

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-xl shadow p-6 mb-6">
        <h2 className="font-bold text-2xl mb-4 text-blue-700">
          Select Numerical Features to Process
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
          {allNumericalFeatureOrder
            .filter((id) => allNumericalItemsMap[id] && !allNumericalItemsMap[id].disabled)
            .map((id) => {
              const item = allNumericalItemsMap[id];
              return (
                <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-3 py-2 shadow">
                  <input
                    type="checkbox"
                    id={item.id}
                    className="accent-blue-500"
                    checked={features.includes(item.feature)}
                    onChange={(e) =>
                      handleFeatureCheckboxChange(item.feature, e.target.checked)
                    }
                  />
                  <span>{item.feature}</span>
                </label>
              );
            })}
        </div>
        <Dropdown
          options={["Trimming", "Capping", "Power Transformation"]}
          placeholder="Select method"
          tell="Select Outlier Handling Method:"
          searchPlaceholder="Search method..."
          value={selectedOutlierOption}
          setValue={handleOptionChange}
        />
      </div>
      {renderContent()}
      <div className="flex flex-col items-center gap-5 p-5 w-full">
        <div className="flex gap-5">
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleApply}
          >
            Apply Outlier Handling
          </button>
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleResetParameters}
          >
            Reset Parameters
          </button>
        </div>
      </div>
      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Outlier"
        />
      )}
    </div>
  );
};

export default Outlier;
